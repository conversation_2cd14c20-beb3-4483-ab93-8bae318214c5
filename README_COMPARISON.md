# Superlet vs DLOGST Spectral Descriptor Comparison

This directory contains scripts for comparing Superlet Transform with DLOGST (Discrete Logarithmic S-Transform) spectral descriptors, creating comprehensive 7-column visualization plots.

## Overview

The comparison scripts generate plots with the following structure:
1. **Column 1**: Original signal
2. **Columns 2-4**: Superlet Transform results (Magnitude, Voice, Magnitude×Voice)
3. **Columns 5-7**: DLOGST results (Magnitude, Voice, Magnitude×Voice)

## Files Created

### Main Comparison Scripts

1. **`test_superlet_vs_dlogst_synthetic.py`**
   - Uses synthetic signals with time-varying frequency content
   - No external data required
   - Best for testing and demonstration

2. **`demo_superlet_dlogst_comparison.py`**
   - Enhanced version of the original demo
   - Multiple signal types available (multi_freq, chirp, modulated, complex)
   - Based on `demo_superlet_full.py` structure

3. **`test_superlet_vs_dlogst_comparison.py`**
   - Works with SEG-Y files (like the original `ex_2nd_Local_fomel_freq_Seismic_Arbitrary.py`)
   - Includes GUI for file selection and parameter adjustment
   - Supports arbitrary line seismic data

### Utility Scripts

4. **`test_basic_functionality.py`**
   - Tests if all required modules can be imported
   - Verifies basic Superlet and DLOGST functionality
   - Run this first to ensure everything is working

5. **`run_comparison.bat`**
   - Windows batch script for easy execution
   - Automatically uses the correct Python environment
   - Menu-driven interface

## Requirements

### Python Environment
The scripts require the PrizmEnv virtual environment to be available at:
```
C:\Users\<USER>\PrizmEnv\
```

### Required Packages
- numpy
- matplotlib
- scipy
- pyfftw
- segyio (for SEG-Y file support)
- tkinter (for GUI dialogs)

## Usage

### Method 1: Using the Batch Script (Recommended)
```batch
run_comparison.bat
```
This will present a menu to choose which script to run.

### Method 2: Direct Python Execution
```batch
C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_superlet_vs_dlogst_synthetic.py
C:\Users\<USER>\PrizmEnv\Scripts\python.exe demo_superlet_dlogst_comparison.py
C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_superlet_vs_dlogst_comparison.py
```

### Method 3: Test Basic Functionality First
```batch
C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_basic_functionality.py
```

## Output

Each script generates high-resolution PNG files:
- `superlet_vs_dlogst_synthetic_comparison.png`
- `superlet_dlogst_comparison_complex.png` (or other signal types)
- `superlet_vs_dlogst_comparison.png` (for SEG-Y data)

## Parameters

### Superlet Transform Parameters
- **c1**: Base number of cycles (default: 3)
- **orders**: Tuple of (min_order, max_order) (default: (1, 10))
- **freqs**: Frequency array for analysis

### DLOGST Parameters
- **shape**: Shape constant (default: 0.35)
- **kmax**: Maximum k value (default: 120)
- **int_val**: Intercept value (default: 35)
- **fmax**: Maximum frequency for analysis

## Signal Types (demo_superlet_dlogst_comparison.py)

1. **multi_freq**: Multiple fixed frequency components
2. **chirp**: Frequency sweep from low to high
3. **modulated**: Amplitude modulated signal
4. **complex**: Time-varying components with different envelopes

## Customization

You can modify the signal generation, parameters, or visualization by editing the respective scripts. Key areas for customization:

- Signal generation functions
- Frequency ranges
- Transform parameters
- Plot styling and colormaps
- Output resolution and format

## Troubleshooting

1. **Import Errors**: Run `test_basic_functionality.py` first
2. **Environment Issues**: Ensure PrizmEnv is properly set up
3. **SEG-Y File Issues**: Check file format and path
4. **Memory Issues**: Reduce signal length or frequency resolution

## Based On

- Original `demo_superlet_full.py`
- `reference/ex_2nd_Local_fomel_freq_Seismic_Arbitrary.py`
- `reference/dlogst_spec_descriptor.py`
- `superletcx.py`

## Notes

- The scripts automatically handle time axis orientation (seismic convention)
- Voice components use the real part of complex transforms
- Magnitude×Voice products provide combined amplitude and phase information
- All plots use consistent frequency ranges for direct comparison
