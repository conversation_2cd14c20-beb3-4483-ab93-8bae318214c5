#!/usr/bin/env python3
"""
Superlet Transform Testing Script

This script demonstrates the application of wavelet and superlet transforms 
for time-frequency analysis using the implementations in superlet.py and superletcx.py.
Based on demo_jax.py but adapted for non-JAX implementations.
"""

import numpy as np
import matplotlib.pyplot as plt
# Configure smaller figure and font sizes for better on-screen display
plt.rcParams.update({
    'font.size': 8,      # Reduce default text size
    'figure.dpi': 120    # Lower DPI so pixel dimensions are smaller
})
import time
from superlet import superlets as superlet_original
from superletcx import superlets as superlet_cx

# Signal generation parameters
fs = 1024  # Sampling rate
burst_freqs = [20, 40, 60]  # Frequencies of signal bursts
f_shift = 10  # Frequency shift for contamination
n_cycles = 11  # Number of cycles for main bursts
n_neighb_cycles = 12  # Number of cycles for neighboring bursts

print("Generating synthetic signal...")

# Generate synthetic signal similar to demo_jax.py
ys = []
for f in burst_freqs:
    # Main burst
    t = 1/f * n_cycles
    x = np.linspace(0, t, int(t * fs))
    y = np.sin(2*np.pi*f*x) + np.sin(2*np.pi*(f+f_shift)*x - np.pi/1.5)
    ys.append(y)
    
    # Neighboring burst
    t2 = 1/f * n_neighb_cycles
    x2 = np.linspace(0, t2, int(t2 * fs))
    y2 = np.sin(2*np.pi*(f+15)*x2) + np.sin(2*np.pi*(f+25)*x2 - np.pi/3)
    ys.append(y2)

# Concatenate all signal components
signal = np.concatenate(ys)

# Add some noise for realism
np.random.seed(42)
noise_level = 0.1
signal += noise_level * np.random.randn(len(signal))

print(f"Generated signal with {len(signal)} samples at {fs} Hz")

# Time vector for plotting
time_vector = np.arange(len(signal)) / fs

# Frequencies for analysis
freqs = np.linspace(10, 80, 141)

# Create figure directory
import os
os.makedirs('figures', exist_ok=True)

# Plot the generated signal
fig, ax = plt.subplots(figsize=(8, 2), dpi=120)
ax.plot(time_vector, signal, linewidth=0.8, color='black')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Amplitude')
ax.set_title('Synthetic Test Signal')
ax.grid(True, alpha=0.3)
ax.set_xlim(0, time_vector[-1])
plt.tight_layout()
plt.savefig('figures/signal.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n" + "="*60)
print("TESTING SUPERLET TRANSFORMS")
print("="*60)

# Test 1: Basic wavelet transform (order=1)
print("\n1. Testing basic wavelet transform (order=1)...")

# Parameters for basic wavelet
cycles = 10

# Using superlet.py (order=1 gives wavelet transform)
spectrum_basic = superlet_original(signal, fs, freqs, cycles, [1])

# Plot basic wavelet transform
fig, ax = plt.subplots(figsize=(7, 3), dpi=120)
im = ax.imshow(np.abs(spectrum_basic), aspect='auto', origin='lower',
               extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
               cmap='viridis')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Frequency (Hz)')
ax.set_title(f'Basic Wavelet Transform (cycles={cycles})')
plt.colorbar(im, ax=ax, label='Magnitude')
plt.tight_layout()
plt.savefig('figures/wavelet_basic.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 2: Superlet transform with different orders
print("\n2. Testing superlet transform with different orders...")

# Parameters for superlet
c1 = 3  # Base cycles
orders = [1, 5, 15, 30]  # Different orders to test

fig, axes = plt.subplots(2, 2, figsize=(9, 6), dpi=120)
axes = axes.flatten()

for i, order in enumerate(orders):
    print(f"   Processing order {order}...")
    
    # Using superlet.py
    start_time = time.time()
    spectrum = superlet_original(signal, fs, freqs, c1, [1, order])
    time_original = time.time() - start_time
    
    # Using superletcx.py
    start_time = time.time()
    spectrum_cx = superlet_cx(signal, fs, freqs, c1, [1, order])
    time_cx = time.time() - start_time
    
    print(f"      superlet.py: {time_original:.3f}s, superletcx.py: {time_cx:.3f}s")
    
    # Plot results from superlet.py
    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'Superlet Transform (c1={c1}, orders=1-{order})')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Superlet Transform Results (superlet.py)', fontsize=14)
plt.tight_layout()
plt.savefig('figures/superlet_orders.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 3: Comparison between superlet.py and superletcx.py
print("\n3. Comparing superlet.py vs superletcx.py...")

# Parameters for comparison
c1_comp = 5
orders_comp = [1, 20]

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)

# superlet.py results
print("   superlet.py...")
spectrum_orig = superlet_original(signal, fs, freqs, c1_comp, orders_comp)

# superletcx.py results  
print("   superletcx.py...")
spectrum_cx = superlet_cx(signal, fs, freqs, c1_comp, orders_comp)

# Plot comparisons
ax1 = axes[0, 0]
im1 = ax1.imshow(np.abs(spectrum_orig), aspect='auto', origin='lower',
                 extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                 cmap='viridis')
ax1.set_title('superlet.py Implementation')
ax1.set_xlabel('Time (s)')
ax1.set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=ax1, label='Magnitude')

ax2 = axes[0, 1]
im2 = ax2.imshow(np.abs(spectrum_cx), aspect='auto', origin='lower',
                 extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                 cmap='viridis')
ax2.set_title('superletcx.py Implementation')
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=ax2, label='Magnitude')

# Difference plot
ax3 = axes[1, 0]
diff = np.abs(spectrum_orig) - np.abs(spectrum_cx)
im3 = ax3.imshow(diff, aspect='auto', origin='lower',
                 extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                 cmap='RdBu_r', vmin=-np.max(np.abs(diff)), vmax=np.max(np.abs(diff)))
ax3.set_title('Difference (superlet.py - superletcx.py)')
ax3.set_xlabel('Time (s)')
ax3.set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=ax3, label='Difference')

# Relative difference
ax4 = axes[1, 1]
rel_diff = 2 * np.abs(spectrum_orig - spectrum_cx) / (np.abs(spectrum_orig) + np.abs(spectrum_cx) + 1e-10)
im4 = ax4.imshow(rel_diff, aspect='auto', origin='lower',
                 extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                 cmap='hot', vmin=0, vmax=0.1)
ax4.set_title('Relative Difference')
ax4.set_xlabel('Time (s)')
ax4.set_ylabel('Frequency (Hz)')
plt.colorbar(im4, ax=ax4, label='Relative Difference')

plt.suptitle(f'Implementation Comparison (c1={c1_comp}, orders={orders_comp})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/implementation_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 4: Parameter exploration
print("\n4. Parameter exploration...")

# Different base cycles to test
c1_values = [1, 3, 5, 10]
order_range = [1, 30]

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)
axes = axes.flatten()

for i, c1_val in enumerate(c1_values):
    print(f"   Testing c1={c1_val}...")
    
    spectrum = superlet_original(signal, fs, freqs, c1_val, order_range)
    
    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'c1={c1_val}, orders={order_range[0]}-{order_range[1]}')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Parameter Exploration: Base Cycles', fontsize=14)
plt.tight_layout()
plt.savefig('figures/parameter_exploration.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 5: Performance comparison
print("\n5. Performance comparison...")

# Test with different signal lengths
lengths = [1000, 5000, 10000, 20000]
c1_perf = 3
orders_perf = [1, 10]

performance_results = []

for length in lengths:
    # Create test signal
    test_signal = np.random.randn(length)
    
    # superlet.py timing
    start_time = time.time()
    _ = superlet_original(test_signal, fs, freqs[:20], c1_perf, orders_perf)
    time_orig = time.time() - start_time
    
    # superletcx.py timing
    start_time = time.time()
    _ = superlet_cx(test_signal, fs, freqs[:20], c1_perf, orders_perf)
    time_cx = time.time() - start_time
    
    performance_results.append({
        'length': length,
        'superlet_py': time_orig,
        'superletcx_py': time_cx,
        'ratio': time_orig / time_cx
    })
    
    print(f"   Length {length}: superlet.py={time_orig:.3f}s, superletcx.py={time_cx:.3f}s, ratio={time_orig/time_cx:.2f}")

# Plot performance results
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(8, 4), dpi=120)

lengths_plot = [r['length'] for r in performance_results]
times_orig = [r['superlet_py'] for r in performance_results]
times_cx = [r['superletcx_py'] for r in performance_results]
ratios = [r['ratio'] for r in performance_results]

ax1.plot(lengths_plot, times_orig, 'o-', label='superlet.py', linewidth=2)
ax1.plot(lengths_plot, times_cx, 's-', label='superletcx.py', linewidth=2)
ax1.set_xlabel('Signal Length')
ax1.set_ylabel('Time (s)')
ax1.set_title('Execution Time Comparison')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_xscale('log')
ax1.set_yscale('log')

ax2.plot(lengths_plot, ratios, 'o-', color='red', linewidth=2)
ax2.axhline(y=1, color='black', linestyle='--', alpha=0.5)
ax2.set_xlabel('Signal Length')
ax2.set_ylabel('Speed Ratio (superlet.py / superletcx.py)')
ax2.set_title('Relative Performance')
ax2.grid(True, alpha=0.3)
ax2.set_xscale('log')

plt.tight_layout()
plt.savefig('figures/performance_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Summary
print("\n" + "="*60)
print("TEST SUMMARY")
print("="*60)
print(f"Signal length: {len(signal)} samples")
print(f"Sampling rate: {fs} Hz")
print(f"Frequency range: {freqs[0]}-{freqs[-1]} Hz")
print(f"Number of frequencies: {len(freqs)}")
print("\nGenerated figures:")
print("   - figures/signal.png")
print("   - figures/wavelet_basic.png")
print("   - figures/superlet_orders.png")
print("   - figures/implementation_comparison.png")
print("   - figures/parameter_exploration.png")
print("   - figures/performance_comparison.png")

print("\nPerformance summary:")
for result in performance_results:
    print(f"   Length {result['length']}: {result['superlet_py']:.3f}s vs {result['superletcx_py']:.3f}s")

print("\nTesting completed successfully!")