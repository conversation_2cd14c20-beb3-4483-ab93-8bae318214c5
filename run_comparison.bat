@echo off
REM Batch script to run the superlet vs DLOGST comparison scripts
REM This script uses the correct Python environment

echo ========================================
echo Superlet vs DLOGST Comparison Scripts
echo ========================================
echo.

echo Available comparison scripts:
echo 1. test_superlet_vs_dlogst_synthetic.py - Uses synthetic signal
echo 2. demo_superlet_dlogst_comparison.py - Uses complex test signal  
echo 3. test_superlet_vs_dlogst_comparison.py - Uses SEG-Y file (requires file selection)
echo 4. test_basic_functionality.py - Basic functionality test
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo Running synthetic signal comparison...
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_superlet_vs_dlogst_synthetic.py
) else if "%choice%"=="2" (
    echo Running demo comparison with complex signal...
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe demo_superlet_dlogst_comparison.py
) else if "%choice%"=="3" (
    echo Running SEG-Y file comparison (will open file dialog)...
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_superlet_vs_dlogst_comparison.py
) else if "%choice%"=="4" (
    echo Running basic functionality test...
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_basic_functionality.py
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Script completed. Check the generated PNG files for results.
pause
