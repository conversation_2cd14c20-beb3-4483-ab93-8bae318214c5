#!/usr/bin/env python3
"""
Enhanced Superlet Demo with DLOGST Comparison
Based on demo_superlet_full.py but extended to include DLOGST spectral descriptor comparison
Creates a 7-column comparison plot as requested
"""

import numpy as np
import matplotlib.pyplot as plt
from superletcx import superlets
from reference.dlogst_spec_descriptor import dlogst_spec_descriptor

def generate_test_signal(fs=1024, duration=2.0, signal_type='multi_freq'):
    """Generate a test signal with known frequency components."""
    t = np.linspace(0, duration, int(fs * duration))
    
    if signal_type == 'multi_freq':
        # Create signal with multiple frequency components (original demo style)
        signal = (np.sin(2 * np.pi * 20 * t) +  # 20 Hz component
                  np.sin(2 * np.pi * 40 * t) +  # 40 Hz component  
                  np.sin(2 * np.pi * 60 * t))   # 60 Hz component
    
    elif signal_type == 'chirp':
        # Frequency sweep from 10 to 80 Hz
        f0, f1 = 10, 80
        signal = np.sin(2 * np.pi * (f0 + (f1 - f0) * t / duration) * t)
    
    elif signal_type == 'modulated':
        # Amplitude modulated signal
        carrier_freq = 40  # Hz
        mod_freq = 2  # Hz
        signal = (1 + 0.5 * np.sin(2 * np.pi * mod_freq * t)) * np.sin(2 * np.pi * carrier_freq * t)
    
    elif signal_type == 'complex':
        # Complex signal with time-varying components
        signal = np.zeros_like(t)
        # Low frequency component throughout
        signal += 0.8 * np.sin(2 * np.pi * 15 * t)
        # Medium frequency with Gaussian envelope
        envelope = np.exp(-((t - duration/2) / (duration/4))**2)
        signal += 0.6 * envelope * np.sin(2 * np.pi * 45 * t)
        # High frequency burst
        burst_mask = (t > duration/4) & (t < duration/2)
        signal[burst_mask] += 0.4 * np.sin(2 * np.pi * 70 * t[burst_mask])
    
    # Add some noise
    np.random.seed(42)
    signal += 0.1 * np.random.randn(len(t))
    
    return t, signal

def create_7_column_comparison(signal, time, fs, freqs, c1=3, orders=(1, 10), 
                              shape=0.35, kmax=120, int_val=35, colormap='viridis'):
    """Create the 7-column comparison plot."""
    
    dt = 1.0 / fs
    fmax_samples = len(signal) // 2
    
    print("Computing Superlet Transform...")
    # Compute Superlet Transform
    superlet_spectrum = superlets(signal, fs, freqs, c1, orders)
    superlet_magnitude = np.abs(superlet_spectrum)
    superlet_voice = np.real(superlet_spectrum)  # Real component for voice with oscillation
    superlet_mag_voice = superlet_magnitude * superlet_voice
    
    print("Computing DLOGST Spectral Descriptor...")
    # Compute DLOGST
    MST, dlogst_mag, dlogst_phase, dlogst_voice, peak_freq, freq_loc, spec_centroid, \
    spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
        dlogst_spec_descriptor(signal, dt, fmax_samples, shape, kmax, int_val)
    
    dlogst_mag_voice = dlogst_mag * dlogst_voice
    
    # Limit frequency range for DLOGST to match superlet range
    freq_limit = np.argmin(np.abs(freqst - freqs[-1]))
    
    # Create the 7-column plot
    fig, axes = plt.subplots(1, 7, figsize=(28, 8), sharey=True)
    fig.suptitle('Superlet vs DLOGST Spectral Descriptor Comparison', fontsize=16)
    
    # Column 1: Original Signal
    axes[0].plot(signal, time, color='black', linewidth=0.8)
    axes[0].set_title('Original Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)
    axes[0].set_ylim(time[0], time[-1])
    axes[0].invert_yaxis()  # Makes time increase downward
    
    # Columns 2-4: Superlet results
    # Magnitude TFR
    im1 = axes[1].imshow(superlet_magnitude.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], time[0], time[-1]],
                         cmap=colormap)
    axes[1].set_title('Superlet Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_ylabel('Time (s)')
    plt.colorbar(im1, ax=axes[1], shrink=0.8)
    
    # Voice TFR (real part with oscillation)
    vmax_superlet = np.max(np.abs(superlet_voice))
    im2 = axes[2].imshow(superlet_voice.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], time[0], time[-1]],
                         cmap='RdBu', vmin=-vmax_superlet, vmax=vmax_superlet)
    axes[2].set_title('Superlet Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_ylabel('Time (s)')
    plt.colorbar(im2, ax=axes[2], shrink=0.8)
    
    # Magnitude * Voice TFR
    im3 = axes[3].imshow(superlet_mag_voice.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], time[0], time[-1]],
                         cmap=colormap)
    axes[3].set_title('Superlet Mag×Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_ylabel('Time (s)')
    plt.colorbar(im3, ax=axes[3], shrink=0.8)
    
    # Columns 5-7: DLOGST results
    # Magnitude TFR
    im4 = axes[4].imshow(dlogst_mag[:freq_limit, :].T, aspect='auto', origin='lower',
                         extent=[freqst[0], freqst[freq_limit-1], time[0], time[-1]],
                         cmap=colormap)
    axes[4].set_title('DLOGST Magnitude')
    axes[4].set_xlabel('Frequency (Hz)')
    axes[4].set_ylabel('Time (s)')
    plt.colorbar(im4, ax=axes[4], shrink=0.8)
    
    # Voice TFR
    vmax_dlogst = np.max(np.abs(dlogst_voice[:freq_limit, :]))
    im5 = axes[5].imshow(dlogst_voice[:freq_limit, :].T, aspect='auto', origin='lower',
                         extent=[freqst[0], freqst[freq_limit-1], time[0], time[-1]],
                         cmap='RdBu', vmin=-vmax_dlogst, vmax=vmax_dlogst)
    axes[5].set_title('DLOGST Voice')
    axes[5].set_xlabel('Frequency (Hz)')
    axes[5].set_ylabel('Time (s)')
    plt.colorbar(im5, ax=axes[5], shrink=0.8)
    
    # Magnitude * Voice TFR
    im6 = axes[6].imshow(dlogst_mag_voice[:freq_limit, :].T, aspect='auto', origin='lower',
                         extent=[freqst[0], freqst[freq_limit-1], time[0], time[-1]],
                         cmap=colormap)
    axes[6].set_title('DLOGST Mag×Voice')
    axes[6].set_xlabel('Frequency (Hz)')
    axes[6].set_ylabel('Time (s)')
    plt.colorbar(im6, ax=axes[6], shrink=0.8)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    return fig, axes

def main():
    """Main function demonstrating the comparison."""
    # Parameters
    fs = 1024
    duration = 2.0
    
    # Signal options: 'multi_freq', 'chirp', 'modulated', 'complex'
    signal_type = 'complex'  # Change this to test different signals
    
    # Generate test signal
    t, signal = generate_test_signal(fs, duration, signal_type)
    
    # Frequency analysis parameters
    freqs = np.linspace(10, 80, 71)
    c1 = 3  # Superlet base cycles
    orders = (1, 10)  # Superlet orders
    
    # DLOGST parameters
    shape = 0.35
    kmax = 120
    int_val = 35
    
    print(f"Analyzing {signal_type} signal...")
    print(f"Duration: {duration}s, Sampling rate: {fs}Hz")
    print(f"Frequency range: {freqs[0]}-{freqs[-1]}Hz")
    
    # Create comparison plot
    fig, axes = create_7_column_comparison(
        signal, t, fs, freqs, c1, orders, 
        shape, kmax, int_val, colormap='viridis'
    )
    
    # Save the figure
    filename = f'superlet_dlogst_comparison_{signal_type}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved as '{filename}'")
    
    plt.show()

if __name__ == "__main__":
    main()
