#!/usr/bin/env python3
"""
Simple Superlet Transform Testing Script

A lightweight version of demo_superlet.py that focuses on core functionality
without extensive plotting. Useful for quick testing and validation.
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from superlet import superlets as superlet_original
from superletcx import superlets as superlet_cx

def generate_test_signal(fs=1024, duration=2.0):
    """Generate a simple test signal with known frequency components."""
    t = np.linspace(0, duration, int(fs * duration))
    
    # Create signal with multiple frequency components
    signal = (np.sin(2 * np.pi * 20 * t) +  # 20 Hz component
              np.sin(2 * np.pi * 40 * t) +  # 40 Hz component  
              np.sin(2 * np.pi * 60 * t))   # 60 Hz component
    
    # Add some noise
    np.random.seed(42)
    signal += 0.1 * np.random.randn(len(t))
    
    return t, signal

def quick_test():
    """Run a quick test of both superlet implementations."""
    print("Superlet Transform Quick Test")
    print("=" * 40)
    
    # Generate test signal
    fs = 1024
    t, signal = generate_test_signal(fs, duration=1.0)
    print(f"Generated signal: {len(signal)} samples at {fs} Hz")
    
    # Define analysis parameters
    freqs = np.linspace(10, 80, 71)  # 10-80 Hz, 71 points
    c1 = 3  # Base cycles
    orders = [1, 10]  # Order range
    
    # Test superlet.py
    print("\nTesting superlet.py...")
    start_time = time.time()
    spectrum_orig = superlet_original(signal, fs, freqs, c1, orders)
    time_orig = time.time() - start_time
    print(f"   Completed in {time_orig:.3f}s")
    
    # Test superletcx.py
    print("Testing superletcx.py...")
    start_time = time.time()
    spectrum_cx = superlet_cx(signal, fs, freqs, c1, orders)
    time_cx = time.time() - start_time
    print(f"   Completed in {time_cx:.3f}s")
    
    # Basic validation
    print("\nValidation:")
    print(f"   Spectrum shape: {spectrum_orig.shape}")
    print(f"   Max magnitude (orig): {np.max(np.abs(spectrum_orig)):.4f}")
    print(f"   Max magnitude (cx): {np.max(np.abs(spectrum_cx)):.4f}")
    
    # Check similarity
    diff = np.abs(spectrum_orig - spectrum_cx)
    max_diff = np.max(diff)
    print(f"   Max difference: {max_diff:.6f}")
    
    # Simple visualization
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # Original implementation
    axes[0, 0].imshow(np.abs(spectrum_orig), aspect='auto', origin='lower',
                      extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                      cmap='viridis')
    axes[0, 0].set_title('superlet.py')
    axes[0, 0].set_ylabel('Frequency (Hz)')
    
    # CX implementation
    axes[0, 1].imshow(np.abs(spectrum_cx), aspect='auto', origin='lower',
                      extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                      cmap='viridis')
    axes[0, 1].set_title('superletcx.py')
    
    # Difference
    im = axes[1, 0].imshow(diff, aspect='auto', origin='lower',
                           extent=[0, len(signal)/fs, freqs[0], freqs[-1]],
                           cmap='hot')
    axes[1, 0].set_title('Absolute Difference')
    axes[1, 0].set_xlabel('Time (s)')
    axes[1, 0].set_ylabel('Frequency (Hz)')
    plt.colorbar(im, ax=axes[1, 0])
    
    # Signal plot
    axes[1, 1].plot(t, signal)
    axes[1, 1].set_title('Test Signal')
    axes[1, 1].set_xlabel('Time (s)')
    axes[1, 1].set_ylabel('Amplitude')
    
    plt.tight_layout()
    plt.savefig('figures/simple_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("\nTest completed successfully!")
    print("Results saved to: figures/simple_test.png")

if __name__ == "__main__":
    quick_test()