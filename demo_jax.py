#!/usr/bin/env python3
# %% cell 1
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# %% cell 2
import jax.numpy as jnp
import matplotlib.pyplot as plt
plt.rcParams.update({'font.size': 8})
from superlets import wavelet_transform, adaptive_superlet_transform

# %% [markdown] cell 3
# We first generate a signal similar to that used in Fig. 3 of the paper. For this demo, we use three bursts 11 cycles long, with frequency 20Hz, 40Hz and 60Hz each. Each burst is "contaminated" by neighbors both in time and frequency. The neighbors in frequency are 10Hz higher than the original values overlapping with the original signal, while the neighbors in time begin 2 cycles later, and last for 12 cycles. The entire signal is generated with a sampling rate of 1024 samples per second.

# %% cell 4
fs = 1024
burst_freqs = [20, 40, 60]
f_shift = 10
n_cycles = 11
n_neighb_cycles = 12
ys = []

# create a 0.1s blank signal to start
ys.append(jnp.zeros(int(fs*0.1)))

for f in burst_freqs:
    # frequency contaminated signal
    t = 1/f * n_cycles
    x = jnp.linspace(0, t, int(t * fs))
    y = jnp.sin(2*jnp.pi*f*x) + jnp.sin(2*jnp.pi*(f+f_shift)*x - jnp.pi/1.5)
    ys.append(y)

    # time contaminated signal, 2 cycles later
    ys.append(jnp.zeros(int(fs*(1/f)*2)))

    t2 = 1/f * n_neighb_cycles
    x = jnp.linspace(0, t2, int(t2 * fs))
    y = jnp.sin(2*jnp.pi*f*x)
    ys.append(y)

    # space between bursts of 0.1s
    ys.append(jnp.zeros(int(fs*0.1)))

signal = jnp.concatenate(ys)

# %% cell 5
fig, ax = plt.subplots(figsize=(10, 1.5), dpi=300)
ax.plot(jnp.linspace(0, len(signal)/fs, len(signal)), signal)
ax.set_title("Generated Signal")
ax.set_xlabel("Time (s)")
plt.tight_layout()
plt.show()

# %% [markdown] cell 6
# We first apply regular wavelet transforms to the signal, with wavelets of 3, 16 and 33 cycles long, respectively. Note that we specifically use the complex Morlet wavelet for both the wavelet and superlet transforms.

# %% cell 7
freqs = jnp.linspace(10, 80, 141)

# %% cell 8
fig, axes = plt.subplots(1, 3, figsize=(10, 3), dpi=300)
for i, c in enumerate([3, 16, 33]):
    scalogram = wavelet_transform(signal, freqs, c, fs)
    im = axes[i].imshow(jnp.abs(scalogram), aspect='auto', origin='lower',
                        extent=[0, len(signal)/fs, freqs[0], freqs[-1]])
    axes[i].set_title(f"Wavelet Transform (cycles={c})")
    axes[i].set_xlabel("Time (s)")
    axes[i].set_ylabel("Frequency (Hz)")
    plt.colorbar(im, ax=axes[i])
plt.tight_layout()
plt.show()

# %% [markdown] cell 9
# There's a tradeoff above; using a low number of cycles results in high temporal resolution, but poor frequency resolution (the activations are "smeared" on the frequency dimension). On the other hand, a high number of cycles results in high frequency resolution, but poor temporal resolution.
#
# There are fundamental limits to how precise we can be on *both* aspects without making a tradeoff on either (the Gabor limit), but the standard wavelet transform isn't on that optimal boundary. Superlets improve resolution on both (time *and* frequency) by taking the geometric mean of wavelet transforms of different cycle lengths, more tightly approaching the Gabor limit.

# %% cell 10
fig, axes = plt.subplots(1, 3, figsize=(10, 3), dpi=300)
for (i, (base_cycle, min_order, max_order)) in enumerate(zip([3, 5, 1], [1, 1, 5], [30, 30, 40])):
    scalogram = adaptive_superlet_transform(signal, freqs, fs, base_cycle, min_order, max_order, mode="add")
    im = axes[i].imshow(jnp.abs(scalogram), aspect='auto', origin='lower',
                        extent=[0, len(signal)/fs, freqs[0], freqs[-1]])
    axes[i].set_title(f"Adaptive Superlet (base={base_cycle}, orders={min_order}-{max_order})")
    axes[i].set_xlabel("Time (s)")
    axes[i].set_ylabel("Frequency (Hz)")
    plt.colorbar(im, ax=axes[i])
plt.tight_layout()
plt.show()